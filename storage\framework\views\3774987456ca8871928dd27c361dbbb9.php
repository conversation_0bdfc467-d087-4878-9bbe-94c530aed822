
<?php $__env->startSection('contentho'); ?>
<?php $__env->startSection('title', 'Transaksi Part'); ?>
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }

    .pagination .page-item {
        margin: 0 2px;
    }

    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }

    #transactions-pagination {
        margin-top: 15px;
    }
</style>
<script>
    // Initial pagination data
    window.transactionsPaginationData = {
        current_page: 1,
        per_page: 15,
        last_page: 1,
        total: 0
    };
</script>
<div class="row bgwhite page-title-box shadow-kit mb-1 rounded-lg">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0"><?php echo e(session('name')); ?></p>
        </div>
    </div>
    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>

<div class="row p-2">
    <div class="col p-4 bgwhite shadow-kit">
        <h4 class="h4 font-bold text-uppercase">Pengiriman Part Ke Site (Status Intransit, Return, Hilang)</h4>
        <div class="row mb-3">
            <div class="col-md-4">
                <select id="siteFilter" class="dropdown btn btn-primary">
                    <option value="">Semua Site</option>
                </select>
            </div>
        </div>
        <table class="table" id="transaction-table">
            <thead class="table-dark text-white">
                <tr>
                    <th class="p-2">ID</th>
                    <th class="p-2">Part Code</th>
                    <th class="p-2">Part Name</th>
                    <th class="p-2">Site</th>
                    <th class="p-2">Tanggal</th>
                    <th class="p-2">Jumlah Dikirim</th>
                    <th class="p-2">Jumlah Diterima</th>
                    <th class="p-2">Status</th>
                    <th class="p-2">Return Stock</th>
                    <th class="p-2">Aksi</th>
                </tr>
            </thead>
            <tbody>
                <!-- Data akan diisi oleh JavaScript -->
            </tbody>
        </table>
        <div id="transactions-pagination" class="mt-3">
            <!-- Custom pagination will be rendered here by JavaScript -->
        </div>
    </div>
    <div class="col max500 bgwhite shadow-kit p-4 ml-4" id="resolve-form" style="display: none;">
        <div>
            <h4 class="font-bold h4 ">Form Selisih</h4>
            <hr>
            <form id="resolve-form-inner">
                <?php echo csrf_field(); ?>
                <input type="hidden" id="resolve-transaction-id" name="transaction_id">
                <div>
                    <label for="discrepancy_resolution">Penjelasan Perubahan</label>
                    <textarea class="form-control" id="discrepancy_resolution" name="discrepancy_resolution" required>-</textarea>
                    <span id="discrepancy_resolution_error" class="error"></span>
                </div>

                <div>
                    <label for="discrepancy_status">Status Perubahan</label>
                    <select class="form-control" id="discrepancy_status" name="discrepancy_status">
                        <option value="investigation">Ubah status</option>
                        <option value="return">Berhasil Return</option>
                        <option value="pending">Pending</option>
                        <option value="hilang">Hilang</option>
                    </select>
                    <span id="discrepancy_status_error" class="error"></span>
                </div>
                <div class="mt-4">
                    <button class="btn btn-primary" type="submit">Simpan Resolusi</button>
                    <button class="btn btn-secondary" type="button" id="cancel-button">Batal</button>
                </div>

            </form>
            <div id="resolve-message"></div>
        </div>
        <div id="message"></div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('resource'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/adminho/transactions_part.js']); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('warehouse.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/warehouse/transactions_part.blade.php ENDPATH**/ ?>