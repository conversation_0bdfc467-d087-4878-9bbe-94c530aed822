[2025-07-02 09:10:13] local.ERROR: Error in updateDetail: Stok tidak mencukupi. Stok tersedia: 2  
[2025-07-02 09:20:24] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-02 09:20:28] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:20:29] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:20:55] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:20:57] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:20:59] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:02] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:06] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:08] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:09] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:51] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-07-02 09:23:51] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-02 09:24:12] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:24:14] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:24:14] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:33:08] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:33:38] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 10:11:42] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-07-02 10:11:43] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:11:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:11:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:11:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:14:10] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:14:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:14:12] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:14:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:14:25] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:14:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:16:07] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:16:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:16:09] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:16:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:37:26] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:37:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:37:27] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:37:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:37:30] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:37:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:37:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:37:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:39:54] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-07-02 10:40:48] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-07-02 10:42:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:42:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:43:28] local.ERROR: Error in updateDetail: Stok tidak mencukupi. Stok tersedia: 0  
[2025-07-02 10:43:55] local.ERROR: Error in updateDetail: Stok tidak mencukupi. Stok tersedia: 0  
[2025-07-02 10:44:36] local.INFO: update selesai  
[2025-07-02 10:44:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:44:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:52:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:52:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:53:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:53:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:53:48] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:53:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:54:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:54:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:55:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:55:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:55:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:55:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:56:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:56:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:56:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:56:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:03:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:03:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:03:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:03:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:03:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:03:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:53] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:06] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:34] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:34] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:21] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:03] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:13] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:09:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:09:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:09:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:09:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:09:45] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:09:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:09:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:09:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:10:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:10:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:10:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:10:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:10:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:10:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:10:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:10:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:12:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:12:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:12:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:12:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:05] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:50] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:02] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:43] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:15:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:15:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:34] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:17:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:17:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:18:30] local.ERROR: Error in getAllRequisitions: Method Illuminate\Database\Eloquent\Collection::pagenate does not exist.  
[2025-07-02 11:18:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:18:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:19:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:19:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:19:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:19:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:20:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:20:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:21:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:21:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:21:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:21:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:24:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:24:56] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:25:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:25:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:26:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:26:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:26:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:26:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:26:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:29:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:29:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:29:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:39:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:39:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:47:00] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-02 11:47:15] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-06-28  
[2025-07-02 11:47:18] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":1108,"quantity":10,"price":"225000.00","status":"Not Ready","is_custom":false,"part_code":"VB-A-5410-PWB","part_name":"V-BELT A 41"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:47:18] local.INFO: Part 0 is regular, validating part_inventory_id  
[2025-07-02 11:47:18] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":1060,"quantity":3,"price":"450000.00","status":"Not Ready","is_custom":false,"part_code":"PA-VA-PWB","part_name":"PULLEY ADJUSTER A"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:47:18] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-07-02 11:47:18] local.INFO: Validating part at index 2 {"part_data":{"part_inventory_id":1039,"quantity":3,"price":"1975000.00","status":"Not Ready","is_custom":false,"part_code":"MFC-HN260-PWB","part_name":"MOTOR FAN HINO FM280JD"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:47:18] local.INFO: Part 2 is regular, validating part_inventory_id  
[2025-07-02 11:47:18] local.INFO: Validating part at index 3 {"part_data":{"part_inventory_id":903,"quantity":3,"price":"3850000.00","status":"Not Ready","is_custom":false,"part_code":"EVP-220-PWB","part_name":"EVAPORATOR A3 ASSY 24V"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:47:18] local.INFO: Part 3 is regular, validating part_inventory_id  
[2025-07-02 11:48:26] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:48:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:48:38] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:48:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:50:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:50:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:50:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:50:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:50:28] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-06-28  
[2025-07-02 11:50:32] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":1108,"quantity":10,"price":"225000.00","status":"Not Ready","is_custom":false,"part_code":"VB-A-5410-PWB","part_name":"V-BELT A 41"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:50:32] local.INFO: Part 0 is regular, validating part_inventory_id  
[2025-07-02 11:50:32] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":1060,"quantity":3,"price":"450000.00","status":"Not Ready","is_custom":false,"part_code":"PA-VA-PWB","part_name":"PULLEY ADJUSTER A"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:50:32] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-07-02 11:50:32] local.INFO: Validating part at index 2 {"part_data":{"part_inventory_id":1039,"quantity":3,"price":"1975000.00","status":"Not Ready","is_custom":false,"part_code":"MFC-HN260-PWB","part_name":"MOTOR FAN HINO FM280JD"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:50:32] local.INFO: Part 2 is regular, validating part_inventory_id  
[2025-07-02 11:50:32] local.INFO: Validating part at index 3 {"part_data":{"part_inventory_id":903,"quantity":3,"price":"3850000.00","status":"Not Ready","is_custom":false,"part_code":"EVP-220-PWB","part_name":"EVAPORATOR A3 ASSY 24V"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:50:32] local.INFO: Part 3 is regular, validating part_inventory_id  
[2025-07-02 11:53:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:06] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 11:54:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:54:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:54:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:54:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:54:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:55:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:55:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:56:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:56:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:56:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:56:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:56:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:56:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 12:01:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 12:01:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 13:59:14] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-06-28  
[2025-07-02 14:23:20] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-07-02 14:23:22] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 14:23:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:23:27] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 14:23:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:23:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:23:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:24:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:24:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:25:21] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:25:21] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:25:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:25:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:27:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:27:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:27:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:27:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:28:34] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:28:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:28:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:28:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:32:53] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:32:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:33:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:33:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:33:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:33:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:17:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:17:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:17:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:17:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
