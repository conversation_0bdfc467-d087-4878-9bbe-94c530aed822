[2025-07-02 09:10:13] local.ERROR: Error in updateDetail: Stok tidak mencukupi. Stok tersedia: 2  
[2025-07-02 09:20:24] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-02 09:20:28] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:20:29] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:20:55] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:20:57] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:20:59] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:02] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:06] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:08] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:09] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:21:51] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-07-02 09:23:51] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-02 09:24:12] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:24:14] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:24:14] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:33:08] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 09:33:38] local.INFO: Manual Invoice raw date override successful - tanggal_invoice: 2025-07-01  
[2025-07-02 10:11:42] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-07-02 10:11:43] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:11:43] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:11:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:11:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:11:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:14:10] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:11] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:14:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:14:12] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:12] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:14:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:14:25] local.INFO: Sites Data Request {"start_date":"2025-06-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-06-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:14:25] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:14:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:16:07] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:16:07] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:16:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:16:09] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:16:10] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:37:26] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:26] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:37:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:37:27] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:37:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:37:30] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 10:37:30] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 10:37:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:37:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:37:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:39:54] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-07-02 10:40:48] local.INFO: User Devi sapang logged in successfully. employee_id: 00002, site_id: WHO  
[2025-07-02 10:42:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:42:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:43:28] local.ERROR: Error in updateDetail: Stok tidak mencukupi. Stok tersedia: 0  
[2025-07-02 10:43:55] local.ERROR: Error in updateDetail: Stok tidak mencukupi. Stok tersedia: 0  
[2025-07-02 10:44:36] local.INFO: update selesai  
[2025-07-02 10:44:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:44:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:52:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:52:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:53:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:53:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:53:48] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:53:49] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:54:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:54:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:55:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:55:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:55:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:55:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:56:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:56:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 10:56:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 10:56:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:03:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:03:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:03:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:03:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:03:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:03:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:53] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:04:54] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:04:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:06] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:30] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:34] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:34] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:05:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:05:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:06:56] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:06:57] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:21] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:38] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:07:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:07:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:03] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:04] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:13] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:14] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:16] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:20] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:08:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:08:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:09:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:09:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:09:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:09:32] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:09:45] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:09:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:09:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:09:55] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:10:00] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:10:00] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:10:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:10:11] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:10:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:10:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:10:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:10:23] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:12:25] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:12:26] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:12:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:12:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:05] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:42] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:50] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:13:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:13:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:02] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:43] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:14:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:14:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:15:16] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:15:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:22] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:24] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:34] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:16:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:16:52] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:17:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:17:20] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:18:30] local.ERROR: Error in getAllRequisitions: Method Illuminate\Database\Eloquent\Collection::pagenate does not exist.  
[2025-07-02 11:18:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:18:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:19:08] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:19:09] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:19:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:19:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:20:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:20:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:21:07] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:21:08] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:21:12] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:21:13] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:24:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:24:56] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:25:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:25:46] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:26:11] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:26:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:26:32] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:26:33] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:26:54] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:29:02] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:29:04] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:29:05] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:39:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:39:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:47:00] local.INFO: User tika logged in successfully. employee_id: 000012, site_id: WHO  
[2025-07-02 11:47:15] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-06-28  
[2025-07-02 11:47:18] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":1108,"quantity":10,"price":"225000.00","status":"Not Ready","is_custom":false,"part_code":"VB-A-5410-PWB","part_name":"V-BELT A 41"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:47:18] local.INFO: Part 0 is regular, validating part_inventory_id  
[2025-07-02 11:47:18] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":1060,"quantity":3,"price":"450000.00","status":"Not Ready","is_custom":false,"part_code":"PA-VA-PWB","part_name":"PULLEY ADJUSTER A"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:47:18] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-07-02 11:47:18] local.INFO: Validating part at index 2 {"part_data":{"part_inventory_id":1039,"quantity":3,"price":"1975000.00","status":"Not Ready","is_custom":false,"part_code":"MFC-HN260-PWB","part_name":"MOTOR FAN HINO FM280JD"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:47:18] local.INFO: Part 2 is regular, validating part_inventory_id  
[2025-07-02 11:47:18] local.INFO: Validating part at index 3 {"part_data":{"part_inventory_id":903,"quantity":3,"price":"3850000.00","status":"Not Ready","is_custom":false,"part_code":"EVP-220-PWB","part_name":"EVAPORATOR A3 ASSY 24V"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:47:18] local.INFO: Part 3 is regular, validating part_inventory_id  
[2025-07-02 11:48:26] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:27] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:48:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:31] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:48:38] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:42] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:43] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:48:46] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:48:50] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:50:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:50:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:50:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:50:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:50:28] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-06-28  
[2025-07-02 11:50:32] local.INFO: Validating part at index 0 {"part_data":{"part_inventory_id":1108,"quantity":10,"price":"225000.00","status":"Not Ready","is_custom":false,"part_code":"VB-A-5410-PWB","part_name":"V-BELT A 41"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:50:32] local.INFO: Part 0 is regular, validating part_inventory_id  
[2025-07-02 11:50:32] local.INFO: Validating part at index 1 {"part_data":{"part_inventory_id":1060,"quantity":3,"price":"450000.00","status":"Not Ready","is_custom":false,"part_code":"PA-VA-PWB","part_name":"PULLEY ADJUSTER A"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:50:32] local.INFO: Part 1 is regular, validating part_inventory_id  
[2025-07-02 11:50:32] local.INFO: Validating part at index 2 {"part_data":{"part_inventory_id":1039,"quantity":3,"price":"1975000.00","status":"Not Ready","is_custom":false,"part_code":"MFC-HN260-PWB","part_name":"MOTOR FAN HINO FM280JD"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:50:32] local.INFO: Part 2 is regular, validating part_inventory_id  
[2025-07-02 11:50:32] local.INFO: Validating part at index 3 {"part_data":{"part_inventory_id":903,"quantity":3,"price":"3850000.00","status":"Not Ready","is_custom":false,"part_code":"EVP-220-PWB","part_name":"EVAPORATOR A3 ASSY 24V"},"is_custom":false,"is_custom_type":"boolean"} 
[2025-07-02 11:50:32] local.INFO: Part 3 is regular, validating part_inventory_id  
[2025-07-02 11:53:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:06] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 11:54:07] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 11:54:07] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:54:10] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:12] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:54:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:15] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:54:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:54:59] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:54:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:55:15] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:55:17] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:56:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:56:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:56:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:56:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 11:56:51] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 11:56:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 12:01:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 12:01:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 13:59:14] local.INFO: Raw date override successful for penawaran - tanggal_penawaran: 2025-06-28  
[2025-07-02 14:23:20] local.INFO: User pwb20212025 logged in successfully using token. employee_id: 666, site_id:   
[2025-07-02 14:23:22] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:22] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 14:23:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:23:27] local.INFO: Sites Data Request {"start_date":"2025-07-01","end_date":"2025-07-31","month":null,"division":null,"site":null} 
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site DH: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = DH and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site IMK: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = IMK and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site PPA: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = PPA and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site TRB: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = TRB and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.ERROR: Error getting PO data for site UDU: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MariaDB server version for the right syntax to use near ' `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists...' at line 1 (Connection: mysql, SQL: select sum(distinct `unit_transaction_id`, `part_inventory_id`) as aggregate from `unit_transaction_parts` where exists (select * from `unit_transactions` where `unit_transaction_parts`.`unit_transaction_id` = `unit_transactions`.`id` and `site_id` = UDU and `created_at` between 2025-07-01 00:00:00 and 2025-07-31 23:59:59 and `status` = belum po))  
[2025-07-02 14:23:27] local.INFO: Sites Data Response {"count":5} 
[2025-07-02 14:23:29] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:23:30] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:23:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:24:49] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:24:51] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:25:21] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:25:21] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:25:24] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:25:25] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:27:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:27:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:27:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:27:45] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:28:34] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:28:35] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:28:44] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:28:44] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:32:53] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:32:53] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:33:29] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:33:31] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 14:33:37] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 14:33:37] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:17:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:17:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:17:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:17:47] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:36:21] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:36:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:36:41] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:36:47] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:36:48] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:37:18] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:37:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:37:21] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:37:22] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:37:35] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:37:36] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:38:17] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:38:18] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:38:40] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:38:41] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:39:28] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:39:28] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:39:57] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:39:59] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:42:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:42:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:43:33] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 15:43:34] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 15:45:09] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP FOREIGN KEY `daily_reports_code_part_foreign`; check that it exists (Connection: mysql, SQL: alter table `daily_reports` drop foreign key `daily_reports_code_part_foreign`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP FOREIGN KEY `daily_reports_code_part_foreign`; check that it exists (Connection: mysql, SQL: alter table `daily_reports` drop foreign key `daily_reports_code_part_foreign`) at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('alter table `da...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('alter table `da...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('alter table `da...')
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(406): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('daily_reports', Object(Closure))
#6 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_01_27_000002_remove_code_part_from_daily_reports.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_01_27_0000...', Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_27_0000...', Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 48, false)
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1091 Can't DROP FOREIGN KEY `daily_reports_code_part_foreign`; check that it exists at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `da...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('alter table `da...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('alter table `da...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('alter table `da...')
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(406): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('daily_reports', Object(Closure))
#8 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_01_27_000002_remove_code_part_from_daily_reports.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_01_27_0000...', Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_27_0000...', Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 48, false)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-02 15:45:46] local.ERROR: The "--skip-migration" option does not exist. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\RuntimeException(code: 0): The \"--skip-migration\" option does not exist. at C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Input\\ArgvInput.php:226)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Input\\ArgvInput.php(153): Symfony\\Component\\Console\\Input\\ArgvInput->addLongOption('skip-migration', '2025_01_27_0000...')
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Input\\ArgvInput.php(88): Symfony\\Component\\Console\\Input\\ArgvInput->parseLongOption('--skip-migratio...')
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Input\\ArgvInput.php(77): Symfony\\Component\\Console\\Input\\ArgvInput->parseToken('--skip-migratio...', true)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Input\\Input.php(53): Symfony\\Component\\Console\\Input\\ArgvInput->parse()
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(238): Symfony\\Component\\Console\\Input\\Input->bind(Object(Symfony\\Component\\Console\\Input\\InputDefinition))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#11 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#12 {main}
"} 
[2025-07-02 15:54:21] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'backlogs' already exists (Connection: mysql, SQL: create table `backlogs` (`id` bigint unsigned not null auto_increment primary key, `unit_code` varchar(50) not null, `hm_found` float(53) null, `problem_description` varchar(255) not null, `backlog_job` varchar(255) not null, `plan_hm` float(53) null, `status` enum('OPEN', 'CLOSED') not null default 'OPEN', `plan_pull_date` datetime null, `notes` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'backlogs' already exists (Connection: mysql, SQL: create table `backlogs` (`id` bigint unsigned not null auto_increment primary key, `unit_code` varchar(50) not null, `hm_found` float(53) null, `problem_description` varchar(255) not null, `backlog_job` varchar(255) not null, `plan_hm` float(53) null, `status` enum('OPEN', 'CLOSED') not null default 'OPEN', `plan_pull_date` datetime null, `notes` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `b...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `b...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `b...')
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('backlogs', Object(Closure))
#6 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_01_30_000001_create_backlogs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_01_30_0000...', Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_30_0000...', Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 48, false)
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'backlogs' already exists at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `b...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `b...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `b...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `b...')
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('backlogs', Object(Closure))
#8 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_01_30_000001_create_backlogs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_01_30_0000...', Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_30_0000...', Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 48, false)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-02 15:54:48] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'backlogs' already exists (Connection: mysql, SQL: create table `backlogs` (`id` bigint unsigned not null auto_increment primary key, `unit_code` varchar(50) not null, `hm_found` float(53) null, `problem_description` varchar(255) not null, `backlog_job` varchar(255) not null, `plan_hm` float(53) null, `status` enum('OPEN', 'CLOSED') not null default 'OPEN', `plan_pull_date` datetime null, `notes` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'backlogs' already exists (Connection: mysql, SQL: create table `backlogs` (`id` bigint unsigned not null auto_increment primary key, `unit_code` varchar(50) not null, `hm_found` float(53) null, `problem_description` varchar(255) not null, `backlog_job` varchar(255) not null, `plan_hm` float(53) null, `status` enum('OPEN', 'CLOSED') not null default 'OPEN', `plan_pull_date` datetime null, `notes` text null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `b...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `b...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `b...')
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('backlogs', Object(Closure))
#6 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_01_30_000001_create_backlogs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_01_30_0000...', Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_30_0000...', Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 49, false)
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'backlogs' already exists at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `b...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `b...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `b...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `b...')
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('backlogs', Object(Closure))
#8 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_01_30_000001_create_backlogs_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_01_30_0000...', Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_01_30_0000...', Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 49, false)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-02 16:05:41] local.ERROR: SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists (Connection: mysql, SQL: create table `invoice_unit_transactions` (`id` bigint unsigned not null auto_increment primary key, `invoice_id` bigint unsigned not null, `unit_transaction_id` bigint unsigned not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists (Connection: mysql, SQL: create table `invoice_unit_transactions` (`id` bigint unsigned not null auto_increment primary key, `invoice_id` bigint unsigned not null, `unit_transaction_id` bigint unsigned not null, `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:825)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `i...', Array, Object(Closure))
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `i...', Array, Object(Closure))
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `i...')
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('invoice_unit_tr...', Object(Closure))
#6 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_04_23_021926_create_invoice_unit_transactions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_04_23_0219...', Object(Closure))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_04_23_0219...', Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 50, false)
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42S01): SQLSTATE[42S01]: Base table or view already exists: 1050 Table 'invoice_unit_transactions' already exists at C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:571)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(571): PDOStatement->execute()
#1 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(812): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `i...', Array)
#2 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(779): Illuminate\\Database\\Connection->runQueryCallback('create table `i...', Array, Object(Closure))
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(560): Illuminate\\Database\\Connection->run('create table `i...', Array, Object(Closure))
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(118): Illuminate\\Database\\Connection->statement('create table `i...')
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(564): Illuminate\\Database\\Schema\\Blueprint->build(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Schema\\Grammars\\MySqlGrammar))
#6 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(418): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('invoice_unit_tr...', Object(Closure))
#8 C:\\xampp\\htdocs\\portalpwb\\database\\migrations\\2025_04_23_021926_create_invoice_unit_transactions_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(507): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(432): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(441): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(40): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(798): Illuminate\\Console\\View\\Components\\Task->render('2025_04_23_0219...', Object(Closure))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(244): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_04_23_0219...', Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(211): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\xampp\\\\htdocs...', 50, false)
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(138): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(117): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(658): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(89): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(213): Illuminate\\Container\\Container->call(Array)
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Command\\Command.php(279): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(182): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(1094): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\xampp\\htdocs\\portalpwb\\artisan(13): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-07-02 16:13:03] local.ERROR: Attempt to read property "part_name" on null {"userId":"2025005","exception":"[object] (ErrorException(code: 0): Attempt to read property \"part_name\" on null at C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\DailyReportController.php:1008)
[stacktrace]
#0 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 1008)
#1 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\DailyReportController.php(1008): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Attempt to read...', 'C:\\\\xampp\\\\htdocs...', 1008)
#2 [internal function]: App\\Http\\Controllers\\DailyReportController->App\\Http\\Controllers\\{closure}(Object(App\\Models\\PartInventory), 7)
#3 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Arr.php(609): array_map(Object(Closure), Array, Array)
#4 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(799): Illuminate\\Support\\Arr::map(Array, Object(Closure))
#5 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php(388): Illuminate\\Support\\Collection->map(Object(Closure))
#6 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Controllers\\DailyReportController.php(1005): Illuminate\\Database\\Eloquent\\Collection->map(Object(Closure))
#7 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(47): App\\Http\\Controllers\\DailyReportController->searchParts(Object(Illuminate\\Http\\Request))
#8 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\DailyReportController), 'searchParts')
#9 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#10 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#11 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\xampp\\htdocs\\portalpwb\\app\\Http\\Middleware\\CheckAdminSite.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckAdminSite->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#52 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#53 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#54 C:\\xampp\\htdocs\\portalpwb\\public\\index.php(17): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#55 C:\\xampp\\htdocs\\portalpwb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\xampp\\\\htdocs...')
#56 {main}
"} 
[2025-07-02 16:13:58] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 16:13:58] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 16:14:19] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 16:14:19] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
[2025-07-02 16:14:39] local.INFO: Best Parts Data Request {"start_date":"2025-07-01","end_date":"2025-07-02","month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-02 23:59:59"} 
[2025-07-02 16:14:40] local.INFO: Best Parts Data Request {"start_date":null,"end_date":null,"month":null,"division":null,"site":null,"used_start_date":"2025-07-01 00:00:00","used_end_date":"2025-07-31 23:59:59"} 
