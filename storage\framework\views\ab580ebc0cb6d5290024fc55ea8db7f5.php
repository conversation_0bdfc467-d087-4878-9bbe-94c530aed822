<?php $__env->startSection('contentho'); ?>
<?php $__env->startSection('title', 'History Transaksi Warehouse'); ?>
<style>
    /* Pagination styling */
    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
        margin-top: 15px;
    }

    .pagination .page-item {
        margin: 0 2px;
    }

    .pagination .page-item .page-link {
        color: #333;
        background-color: #fff;
        border: 1px solid #ddd;
        padding: 6px 12px;
        text-decoration: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .pagination .page-item.active .page-link {
        color: #fff;
        background-color: #28a745;
        border-color: #28a745;
    }

    .pagination .page-item .page-link:hover {
        background-color: #f8f9fa;
    }

    #transactions-pagination {
        margin-top: 15px;
    }
</style>
<script>
    // Initial pagination data
    window.transactionsPaginationData = {
        current_page: 1,
        per_page: 15,
        last_page: 1,
        total: 0
    };
</script>
<div class="row bgwhite page-title-box shadow-kit mb-1">
    <div class="col d-flex align-items-center" style="max-width: fit-content;">
        <button class="button-menu-mobile text-blue-500 btn-sm">
            <i style="font-size: 30px;" class="mdi mdi-menu"></i>
        </button>
        <div class="text">
            <p class="font-bold m-0"><?php echo e(session('name')); ?></p>
        </div>
    </div>

    <div class="col">
        <ul class="list-unstyled topnav-menu float-right mb-0">
            <li class="dropdown notification-list">
                <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="mdi mdi-bell-outline noti-icon"></i>
                    <span class="badge badge-danger badge-pill float-right badgenotif"></span>
                </a>
                <div class="dropdown-menu dropdown-menu-end dropdown-lg">
                    <div class="dropdown-item noti-title bg-primary">
                        <h5 class="m-0 text-white d-flex justify-content-between align-items-center">
                            Notifikasi
                            <a href="javascript:void(0);" class="text-white" id="clear-all">
                                <small></small>
                            </a>
                        </h5>
                    </div>
                    <div class="noti-scroll" id="notification-list">
                        <!-- Notifications will be dynamically inserted here -->
                    </div>
                </div>
            </li>
        </ul>
    </div>
</div>
<div class="row p-2">
    <div class="col bgwhite shadow-kit p-4 rounded-lg">
        <h1 class="text-xl font-bold mb-4 text-uppercase">History Transaksi Warehouse</h1>
        <div class="mb-4">
            <div class="d-flex flex-wrap gap-3">
                <div>
                    <label for="status_filter" class="form-label">Status:</label>
                    <select class="form-control" id="status_filter">
                        <option value="">Semua Status</option>
                        <option value="intransit">In Transit</option>
                        <option value="return">Return Ke HO</option>
                        <option value="pending">Pending</option>
                        <option value="selesai">Selesai</option>
                    </select>
                </div>
                <div>
                    <label class="form-label">Periode Transaksi:</label>
                    <div class="d-flex gap-2">
                        <input type="date" class="form-control" id="start_date">
                        <input type="date" class="form-control" id="end_date">
                    </div>
                </div>
                <div>
                    <label for="search" class="form-label">Cari Part:</label>
                    <input type="text" class="form-control" id="search" placeholder="Cari part...">
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead class="table-dark text-white">
                    <tr>
                        <th>ID</th>
                        <th>Part Code</th>
                        <th>Part Name</th>
                        <th>Tanggal</th>
                        <th>Qty Kirim Warehouse</th>
                        <th>Qty Terima Site</th>
                        <th>Status</th>
                        <th>Lampiran</th>
                    </tr>
                </thead>
                <tbody id="transaction-table-body">
                </tbody>
            </table>
            <div id="pagination-container"></div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('resource'); ?>
<?php echo app('Illuminate\Foundation\Vite')(['resources/js/adminho/Transactionhistory.js']); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('warehouse.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/warehouse/transaction_history.blade.php ENDPATH**/ ?>