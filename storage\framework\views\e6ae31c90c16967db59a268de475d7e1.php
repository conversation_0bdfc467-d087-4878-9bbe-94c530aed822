<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>Portal PWB - Price List</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Portal PWB" name="description" />
    <meta content="PWB" name="author" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/logo-small.png')); ?>">
    <!-- Styles -->
    <?php echo app('Illuminate\Foundation\Vite')([
        'resources/assets/css/bootstrap.min.css',
        'resources/assets/css/icons.min.css',
        'resources/assets/css/app.min.css',
        'resources/css/app.css',
        'resources/css/superadmin-dashboard.css',
        'resources/css/superadmin-scaling.css',
        'resources/css/superadmin-pricelist.css'
    ]); ?>
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jQuery-slimScroll/1.3.8/jquery.slimscroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/metisMenu/3.0.7/metisMenu.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Vite JS Resources -->
    <?php echo app('Illuminate\Foundation\Vite')([
        'resources/js/superadmin-price-list.js',
        'resources/js/superadmin-scaling.js',
        'resources/js/superadmin-mobile-menu.js'
    ]); ?>
    <style>
        body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            background: url('<?php echo e(asset('assets/images/homewalpaper.jpg')); ?>');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            font-family: 'Segoe UI', sans-serif;
            position: relative;
            overflow-x: hidden;
        }

        .dashboard-container {
            width: 100%;
            max-width: 1600px;
            padding: 0 15px;
            margin: 0 auto;
            /* Add this */
        }

        .price-list-table {
            font-size: 14px;
        }

        * {
            font-size: 14px !important;
        }
    </style>
</head>

<body>
    <!-- Dashboard Container -->
    <div class="container">
        <!-- Login Theme Header -->
        <header class="login-theme-header">
            <!-- Company Logo (Visible on all devices) -->
            <div class="company-logo">
                <img src="<?php echo e(asset('assets/images/logo-small.png')); ?>" alt="PWB Logo">
                <h1 class="company-name">PT. PUTERA WIBOWO BORNEO</h1>
            </div>

            <!-- Mobile Header Top (Only visible on mobile) -->
            <div class="header-top">
                <div class="company-logo">
                    <img src="<?php echo e(asset('assets/images/logo-small.png')); ?>" alt="PWB Logo">
                    <h1 class="company-name" style="font-size: 1.8rem !important;">PT. PUTERA WIBOWO BORNEO</h1>
                </div>

                <!-- Mobile Menu Toggle Button -->
                <button type="button" class="mobile-menu-toggle" id="mobileMenuToggle">
                    <i class="mdi mdi-menu"></i>
                </button>
            </div>

            <!-- Mobile Menu Overlay -->
            <div class="mobile-menu-overlay" id="mobileMenuOverlay"></div>

            <!-- Header Right (Navigation) -->
            <div class="header-right" id="mobileMenu">
                <!-- Close Button for Mobile -->
                <button type="button" class="mobile-menu-close d-lg-none" id="mobileMenuClose">
                    <i class="mdi mdi-close"></i>
                </button>

                <div class="mobile-menu-header d-lg-none">
                    <h5 class="menu-title">Menu Navigasi</h5>
                </div>

                <div class="nav-links">
                    <a href="<?php echo e(route('superadmin.dashboard')); ?>"
                        class="nav-link <?php echo e(request()->routeIs('superadmin.dashboard') ? 'active' : ''); ?>">
                        <i class="mdi mdi-view-dashboard"></i> <span>Dashboard</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.invoices')); ?>"
                        class="nav-link <?php echo e(request()->routeIs('superadmin.invoices') ? 'active' : ''); ?>">
                        <i class="mdi mdi-file-document-outline"></i> <span>acount receveable</span>
                    </a>
                    <a href="<?php echo e(route('superadmin.parts')); ?>"
                        class="nav-link <?php echo e(request()->routeIs('superadmin.parts') ? 'active' : ''); ?>">
                        <i class="mdi mdi-package-variant"></i> <span>Part</span>
                    </a>
                    <!-- <a href="<?php echo e(route('superadmin.part-analysis')); ?>" class="nav-link <?php echo e(request()->routeIs('superadmin.part-analysis') ? 'active' : ''); ?>">
                        <i class="mdi mdi-chart-line"></i> <span>Part Analysis</span>
                    </a> -->
                    <a href="<?php echo e(route('superadmin.price-list')); ?>"
                        class="nav-link <?php echo e(request()->routeIs('superadmin.price-list') ? 'active' : ''); ?>">
                        <i class="mdi mdi-tag-multiple"></i> <span>Price List</span>
                    </a>
                    <a href="<?php echo e(route('logout')); ?>" class="nav-link nav-link-danger">
                        <i class="mdi mdi-logout"></i> <span>Logout</span>
                    </a>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <div class="content-wrapper">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h4 class="card-title mb-0">
                            <i class="mdi mdi-tag-multiple me-1"></i> Daftar Harga Part
                        </h4>
                        <button type="button" class="btn btn-outline-danger btn-sm p-1 m-1" id="export-price-list-btn">
                            <i class="mdi mdi-download me-1"></i> Export
                        </button>
                    </div>
                    <!-- Part Type Filter Buttons -->
                    <div class="d-flex justify-content-between flex-wrap">
                        <div class="filter-buttons part-type-filters mb-3">
                            <button type="button" class="btn btn-outline-primary active" data-part-type="all">
                                Semua Tipe
                            </button>
                            <!-- Part type buttons will be added here by JavaScript -->
                        </div>

                        <!-- Search Box -->
                        <div class="search-box mb-3">
                            <i class="mdi mdi-magnify search-icon"></i>
                            <input type="text" id="searchInput" class="form-control" placeholder="Cari part...">
                        </div>
                    </div>

                    <!-- Site Filter Buttons -->
                    <div class="filter-buttons site-filters mb-3">
                        <?php $__currentLoopData = $sites; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $site): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <button type="button"
                                class="btn <?php echo e($site->site_id == 'WHO' ? 'btn-outline-dark-brown' : 'btn-outline-amber'); ?> <?php echo e($site->site_id == 'WHO' ? 'active' : ''); ?>"
                                data-site-id="<?php echo e($site->site_id); ?>">
                                <?php echo e($site->site_name); ?>

                            </button>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Price List Table -->
                    <div class="table-responsive">
                        <table class="table price-list-table">
                            <thead>
                                <tr>
                                    <th class="py-2">No</th>
                                    <th class="py-2">Kode Part</th>
                                    <th class="py-2">Nama Part</th>
                                    <th class="py-2">Tipe</th>
                                    <th class="py-2">Harga</th>
                                </tr>
                            </thead>
                            <tbody id="priceListTableBody">
                                <!-- Table content will be loaded by JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex" style="justify-content: space-between">
                        <div class="a"></div>
                        <div class="b">
                            <div class="flex">
                                <div class="form-group p-0 m-0">
                                    <select class="btn btn-outline-primary p-1 mx-2 my-0" name="lenght_page"
                                        id="lenght_page">
                                        <option value="10">10</option>
                                        <option value="20" selected>20</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                        <option value="5000">Semua</option>
                                    </select>
                                </div>
                                <div id="pagination-container">
                                    <!-- Pagination will be added by JavaScript -->
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Price List Modal -->
    <div class="modal fade" id="exportPriceListModal" tabindex="-1" aria-labelledby="exportPriceListModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportPriceListModalLabel">
                        <i class="mdi mdi-download me-2"></i>Export Data Price List
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p class="mb-3">Pilih format export yang diinginkan:</p>
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-danger" id="export-price-list-pdf-btn">
                            <i class="mdi mdi-file-pdf-box me-2"></i>Export ke PDF
                        </button>
                        <button type="button" class="btn btn-success" id="export-price-list-excel-btn">
                            <i class="mdi mdi-file-excel me-2"></i>Export ke Excel
                        </button>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="mdi mdi-information-outline me-1"></i>
                            Export akan menggunakan filter yang sedang aktif (site, tipe part, dan pencarian).
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                </div>
            </div>
        </div>
    </div>

    <!-- App Scripts -->
    <script src="<?php echo e(asset('assets/js/vendor.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/app.min.js')); ?>"></script>
</body>

</html><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/superadmin/price-list.blade.php ENDPATH**/ ?>