.skeleton-loader {
    animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-row {
    height: 20px;
    background-color: #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

.image-preview {
    position: relative;
    display: inline-block;
    margin: 5px;
}

.image-preview img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.image-preview .remove-image {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    cursor: pointer;
}

/* Unit dropdown styles */
#unit_dropdown, #modal_unit_dropdown, #edit_modal_unit_dropdown {
    z-index: 1050;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

#unit_dropdown .dropdown-item, #modal_unit_dropdown .dropdown-item, #edit_modal_unit_dropdown .dropdown-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

#unit_dropdown .dropdown-item:hover, #modal_unit_dropdown .dropdown-item:hover, #edit_modal_unit_dropdown .dropdown-item:hover {
    background-color: #f8f9fa;
}

#unit_dropdown .dropdown-item:last-child, #modal_unit_dropdown .dropdown-item:last-child, #edit_modal_unit_dropdown .dropdown-item:last-child {
    border-bottom: none;
}

/* Job and technician list item styles */
.job-item, .technician-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
}

.job-item.highlighted {
    color: rgb(47, 13, 150);
}

/* Highlighted text only - no table row colors */
.highlighted-job-text {
    color: rgb(65, 13, 105);
    font-weight: bold;
}

.job-item .job-info {
    display: flex;
    align-items: center;
}

.job-item .highlight-badge {
    color: #ffc107;
    font-weight: bold;
}

.remove-btn {
    /* background: #ffffff; */
    color: rgb(30, 5, 5);
    background: #ccc3e5;
    border: none;
    border-radius: 10%;
    width: 20px;
    height: 20px;
    font-size: 10px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-btn:hover {
    background: hsl(354, 100%, 50%);
    font-weight: bold;
    border-radius: 50%;
    color: white;
}


#add-daily-report-modal .modal-dialog.modal-fullscreen,
#edit-daily-report-modal .modal-dialog.modal-fullscreen {
    width: 100vw;       /* Full viewport width */
    max-width: none;    /* Remove any max-width constraint */
    height: 100vh;      /* Full viewport height */
    margin: 0;          /* Remove any margins */
    padding: 0;         /* Remove any padding around the dialog */
}

/* Ensure the modal content fills the dialog and has no borders/radius */
#add-daily-report-modal .modal-dialog.modal-fullscreen .modal-content,
#edit-daily-report-modal .modal-dialog.modal-fullscreen .modal-content {
    height: 100%;       /* Content fills the 100vh dialog */
    border-radius: 0;   /* No rounded corners for a fullscreen experience */
    border: 0;          /* No border for a fullscreen experience */
}

/* Ensure the modal body can scroll if its content overflows */
#add-daily-report-modal .modal-dialog.modal-fullscreen .modal-body,
#edit-daily-report-modal .modal-dialog.modal-fullscreen .modal-body {
    overflow-y: auto;
}

/* Optional: Ensure header and footer also don't have rounded corners
   Bootstrap's modal-fullscreen usually handles this, but just in case. */
#add-daily-report-modal .modal-dialog.modal-fullscreen .modal-header,
#add-daily-report-modal .modal-dialog.modal-fullscreen .modal-footer,
#edit-daily-report-modal .modal-dialog.modal-fullscreen .modal-header,
#edit-daily-report-modal .modal-dialog.modal-fullscreen .modal-footer {
    border-radius: 0;
}

/* Table-like form layout styles */
.table-borderless td {
    border: none !important;
    padding: 15px;
}

.table-borderless td:first-child {
    border-right: 1px solid #dee2e6 !important;
}

/* Image upload 3-column layout */
.image-upload-column {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    transition: all 0.3s ease;
}

.image-upload-column:hover {
    background-color: #e9ecef;
    border-color: #6c757d;
}

.image-upload-column .mdi {
    color: #6c757d;
    margin-bottom: 10px;
}

.image-upload-column h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 10px;
}

.image-upload-column .form-control-sm {
    font-size: 0.875rem;
}

.image-upload-column small {
    display: block;
    margin-top: 5px;
    color: #6c757d;
}

/* Minimalist design improvements */
.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.form-control {
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 8px 12px;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #225297;
    box-shadow: 0 0 0 0.2rem rgba(34, 82, 151, 0.25);
}

/* Clean button styling */
.btn-primary {
    background-color: #225297;
    border-color: #225297;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-primary:hover {
    background-color: #1a3f73;
    border-color: #1a3f73;
}

/* Highlighted job text styling */
.highlighted-job-text {
    color: rgb(91, 12, 181);
}

/* Table styling improvements */
.table th {
    background-color: #343a40;
    color: white;
    font-weight: 600;
    border: none;
    padding: 12px 8px;
    font-size: 0.9em;
}

.table td {
    padding: 10px 8px;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
    font-size: 0.9em;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Sortable header styling */
.sortable {
    user-select: none;
    transition: background-color 0.2s ease;
}

.sortable:hover {
    background-color: #495057 !important;
}

.sortable i {
    margin-left: 5px;
    font-size: 0.8em;
    opacity: 0.7;
}

.sortable i.mdi-sort-ascending,
.sortable i.mdi-sort-descending {
    opacity: 1;
    color: #ffc107;
}

/* List styling in table cells */
.table ul {
    margin: 0;
    padding-left: 15px;
    list-style-type: disc;
}

.table ul li {
    margin-bottom: 2px;
    font-size: 0.85em;
    line-height: 1.3;
}

/* Button group improvements */
.btn-group .btn {
    margin-right: 2px;
    border-radius: 4px !important;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .table-borderless td {
        display: block;
        width: 100% !important;
        border-right: none !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .image-upload-column {
        margin-bottom: 15px;
    }

    .table-responsive {
        font-size: 0.8em;
    }

    .table th,
    .table td {
        padding: 8px 4px;
    }

    .btn-group {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 2px;
    }
}

/* Part suggestions dropdown styling */
.modal-part-suggestions,
.edit_modal-part-suggestions {
    position: absolute;
    z-index: 1000;
    background: white;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    max-height: 400px;
    overflow-y: auto;
    width: 100%;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.part-suggestion {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
    font-size: 12px;
    transition: background-color 0.15s ease;
}

.part-suggestion:hover {
    background-color: #f8f9fa;
}

.part-suggestion:last-child {
    border-bottom: none;
}

/* Part problems list styling */
#modal_part_problems_list,
#edit_modal_part_problems_list {
    min-height: 60px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 10px;
}

#modal_part_problems_list .d-flex,
#edit_modal_part_problems_list .d-flex {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 8px;
    margin-bottom: 8px;
}

#modal_part_problems_list .d-flex:last-child,
#edit_modal_part_problems_list .d-flex:last-child {
    margin-bottom: 0;
}

